<template>
  <!-- 菜单栏 -->
  <q-header class="menubar">
    <MenuBar />
    <ToolBar />
  </q-header>

  <q-page class="modeler-page">
    <NodeModelEditor ref="editorRef" :connectionType="connectionType" />
  </q-page>


  <!-- 状态栏 -->
  <q-footer class="statusbar">
    <StatusBar />
  </q-footer>

  <!-- 属性编辑对话框 -->
  <AttributeDialog />

  <!-- 连接属性对话框 -->
  <ConnectionDialog />
</template>

<script setup>
import { ref, provide } from 'vue'
import NodeModelEditor from "components/node_editor/NodeModelEditor.vue";
import AttributeDialog from "components/node_editor/AttributeDialog.vue";
import StatusBar from "components/node_editor/StatusBar.vue";
import ConnectionDialog from "components/node_editor/ConnectionDialog.vue";
import ToolBar from "components/node_editor/ToolBar.vue";
import MenuBar from "components/node_editor/MenuBar.vue";

const editorRef = ref(null)

// 提供编辑器实例给子组件使用
provide('editorRef', editorRef)

const connectionType = ref("packet_stream");

</script>

<style lang="scss" scoped>
.modeler-page {
  height: calc(100vh - 36px - 48px - 24px); /* 减去菜单栏、工具栏和状态栏的高度 */
  overflow: hidden;
}

.menubar {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.statusbar {
  height: 24px;
  min-height: 24px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
}


</style>
