<template>
  <!-- 右键菜单 -->
  <q-menu
    v-model="editorStore.contextMenu.visible"
    :target="true"
    context-menu
    anchor="top left"
    self="top left"
    auto-close
  >
    <q-list dense style="min-width: 160px">
      <!-- 节点右键菜单 -->
      <template v-if="editorStore.contextMenu.targetType === 'node'">
        <q-item clickable v-close-popup @click="renameNode" v-if="editorStore.contextMenu.selectedCells.length === 1">
          <q-item-section avatar>
            <q-icon name="edit" />
          </q-item-section>
          <q-item-section>重命名</q-item-section>
        </q-item>
        <q-item clickable v-close-popup @click="editNodeProperties" v-if="editorStore.contextMenu.selectedCells.length === 1">
          <q-item-section avatar>
            <q-icon name="settings" />
          </q-item-section>
          <q-item-section>编辑属性</q-item-section>
        </q-item>
        <q-separator />
        <q-item clickable v-close-popup @click="cutSelection">
          <q-item-section avatar>
            <q-icon name="content_cut" />
          </q-item-section>
          <q-item-section>剪切</q-item-section>
          <q-item-section side>
            <q-item-label caption>Ctrl+X</q-item-label>
          </q-item-section>
        </q-item>
        <q-item clickable v-close-popup @click="copySelection">
          <q-item-section avatar>
            <q-icon name="content_copy" />
          </q-item-section>
          <q-item-section>复制</q-item-section>
          <q-item-section side>
            <q-item-label caption>Ctrl+C</q-item-label>
          </q-item-section>
        </q-item>
        <q-separator />
        <q-item clickable v-close-popup @click="deleteSelection">
          <q-item-section avatar>
            <q-icon name="delete" />
          </q-item-section>
          <q-item-section>删除节点</q-item-section>
          <q-item-section side>
            <q-item-label caption>Del</q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <!-- 连线右键菜单 -->
      <template v-if="editorStore.contextMenu.targetType === 'edge'">
        <q-item clickable v-close-popup @click="editEdgeProperties">
          <q-item-section avatar>
            <q-icon name="settings" />
          </q-item-section>
          <q-item-section>编辑属性</q-item-section>
        </q-item>
        <q-separator />
        <q-item clickable v-close-popup @click="deleteSelection">
          <q-item-section avatar>
            <q-icon name="delete" />
          </q-item-section>
          <q-item-section>删除连线</q-item-section>
          <q-item-section side>
            <q-item-label caption>Del</q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <!-- 空白区域右键菜单 -->
      <template v-if="editorStore.contextMenu.targetType === 'blank'">
        <q-item
          clickable
          v-close-popup
          @click="pasteFromClipboard"
          :disable="!graph || graph.isClipboardEmpty()"
        >
          <q-item-section avatar>
            <q-icon name="content_paste" />
          </q-item-section>
          <q-item-section>粘贴</q-item-section>
          <q-item-section side>
            <q-item-label caption>Ctrl+V</q-item-label>
          </q-item-section>
        </q-item>
        <q-separator />
        <q-item clickable v-close-popup @click="selectAllNodes">
          <q-item-section avatar>
            <q-icon name="select_all" />
          </q-item-section>
          <q-item-section>全选</q-item-section>
          <q-item-section side>
            <q-item-label caption>Ctrl+A</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-list>
  </q-menu>
</template>

<script setup>
import {onMounted, onUnmounted, watchEffect} from 'vue'
import { useQuasar } from 'quasar'
import { useEditorStore } from 'stores/editor.js'
import { getModuleTypeName, getConnectionTypeName } from 'src/utils/modules.js'
import { portManager } from 'src/utils/port-manager.js'

const $q = useQuasar()
const editorStore = useEditorStore()

// Props
const props = defineProps({
  graph: {
    type: Object,
    required: true
  }
})

// 显示右键菜单
const showContextMenu = (e, targetType, targetCell) => {
  editorStore.contextMenu.visible = false
  editorStore.contextMenu.targetType = targetType
  editorStore.contextMenu.targetCell = targetCell

  // 右键逻辑：如果点击的是已经选中的元素，保持选中不变弹出菜单
  //         如果点击的是未选中的元素，清空其他选中，选中该元素并弹出菜单
  if (targetCell && !props.graph.isSelected(targetCell)) {
    props.graph.cleanSelection()
    props.graph.select(targetCell)
  }

  editorStore.contextMenu.selectedCells = props.graph.getSelectedCells()

  // 延迟显示菜单，确保位置计算正确
  setTimeout(() => {
    editorStore.contextMenu.visible = true
    // console.log("🎯 右键菜单显示成功", editorStore.contextMenu)
  }, 10)
}

// 右键菜单操作方法
const renameNode = () => {
  const node = editorStore.contextMenu.targetCell
  if (!node) return

  const currentName = node.getData()?.name || ''
  $q.dialog({
    title: '输入',
    message: '请输入新的节点名称:',
    prompt: {
      model: currentName,
      type: 'text'
    },
    ok: '确定',
    cancel: '取消',
    persistent: true
  }).onOk(newName => {
    if (newName && newName !== currentName) {
      // 检查名称是否已存在
      const existingNames = props.graph.getNodes()
        .filter(n => n.id !== node.id)
        .map(n => n.getData()?.name || '')

      if (existingNames.includes(newName)) {
        $q.notify({
          message: '节点名称已存在，请使用其他名称',
          color: 'negative',
          position: 'top'
        })
        return
      }

      // 更新节点名称
      const nodeData = node.getData() || {}
      nodeData.name = newName
      node.setData(nodeData)
      node.setAttrs({
        label: {
          text: newName
        }
      })

      editorStore.hasUnsavedChanges = true
    }
  })
}

const editNodeProperties = () => {
  const node = editorStore.contextMenu.targetCell
  if (node) {
    const nodeData = JSON.parse(JSON.stringify(node.getData())) || {}
    editorStore.attributeDialog.title = `${getModuleTypeName(nodeData.type)} 属性`
    editorStore.attributeDialog.data = nodeData
    editorStore.attributeDialog.node = node
    editorStore.attributeDialog.visible = true
  }
}

const editEdgeProperties = () => {
  const edge = editorStore.contextMenu.targetCell
  if (edge) {
    editorStore.connectionDialog.edge = edge
    editorStore.connectionDialog.data = JSON.parse(JSON.stringify(edge.getData()) || {})
    editorStore.connectionDialog.title = `${getConnectionTypeName(editorStore.connectionDialog.data.type)} 属性`
    editorStore.connectionDialog.availableSourcePorts = portManager.getAvailablePorts(
      edge.getSourceNode(), 'output', editorStore.connectionDialog.data.type
    )
    editorStore.connectionDialog.availableTargetPorts = portManager.getAvailablePorts(
      edge.getTargetNode(), 'input', editorStore.connectionDialog.data.type
    )
    editorStore.connectionDialog.visible = true
  }
}

const cutSelection = () => {
  if (props.graph) {
    const cells = props.graph.getSelectedCells()
    if (cells.length) {
      props.graph.copy(cells)
      props.graph.removeCells(cells)
    }
  }
}

const copySelection = () => {
  if (props.graph) {
    const cells = props.graph.getSelectedCells()
    if (cells.length) {
      props.graph.copy(cells)
    }
  }
}

const deleteSelection = () => {
  if (props.graph) {
    const cells = props.graph.getSelectedCells()
    if (cells.length) {
      props.graph.removeCells(cells)
    }
  }
}

const pasteFromClipboard = () => {
  if (props.graph && !props.graph.isClipboardEmpty()) {
    const cells = props.graph.paste({offset: 32})
    props.graph.cleanSelection()
    props.graph.select(cells)
  }
}

const selectAllNodes = () => {
  if (props.graph) {
    const nodes = props.graph.getNodes()
    if (nodes.length) {
      props.graph.select(nodes)
    }
  }
}

// 绑定右键事件
const bindContextMenuEvents = () => {
  if (!props.graph) return

  // 右键菜单事件
  props.graph.on('node:contextmenu', ({e, node}) => {
    e.preventDefault()
    showContextMenu(e, 'node', node)
  })

  props.graph.on('edge:contextmenu', ({e, edge}) => {
    e.preventDefault()
    showContextMenu(e, 'edge', edge)
  })

  props.graph.on('blank:contextmenu', ({e}) => {
    e.preventDefault()
    showContextMenu(e, 'blank', null)
  })

  // 画布点击时隐藏菜单
  props.graph.on('blank:click', () => {
    editorStore.contextMenu.visible = false
  })
}

// 全局点击事件处理器
const handleGlobalClick = (e) => {
  // 如果点击的不是右键菜单，则隐藏菜单
  if (!e.target.closest('.q-menu')) {
    editorStore.contextMenu.visible = false
  }
}

watchEffect(() => {
  console.log('graph changed', props.graph)
  if (props.graph) {
    bindContextMenuEvents()
  }
  // 添加全局点击事件监听器
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  // 移除全局点击事件监听器
  document.removeEventListener('click', handleGlobalClick)
})

// 暴露方法给父组件
defineExpose({
  showContextMenu,
  bindContextMenuEvents
})
</script>

<style lang="scss" scoped>
// 右键菜单样式优化
:deep(.q-menu) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 6px;

  .q-item {
    min-height: 32px;
    padding: 4px 12px;

    &:hover {
      background-color: #f5f5f5;
    }

    .q-item__section--avatar {
      min-width: 24px;
      padding-right: 8px;

      .q-icon {
        font-size: 16px;
        color: #666;
      }
    }

    .q-item__section--side {
      .q-item__label--caption {
        color: #999;
        font-size: 11px;
      }
    }
  }

  .q-separator {
    margin: 4px 0;
  }
}
</style>
