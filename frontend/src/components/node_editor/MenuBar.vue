<template>
  <q-bar class="bg-black text-white">
    <div class="cursor-pointer non-selectable menu-section">
      文件
      <q-menu>
        <q-list dense style="min-width: 180px">
          <q-item clickable v-close-popup @click="fileActions.newModel">
            <q-item-section>新建</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="fileActions.openModel">
            <q-item-section>打开...</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="fileActions.saveModel">
            <q-item-section>保存</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="fileActions.saveAsModel">
            <q-item-section>另存为...</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </div>

    <div class="cursor-pointer non-selectable menu-section">
      编辑
      <q-menu>
        <q-list dense style="min-width: 180px">
          <q-item clickable v-close-popup @click="editActions.undo">
            <q-item-section>撤销</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="editActions.redo">
            <q-item-section>重做</q-item-section>
          </q-item>
          <q-separator/>
          <q-item clickable v-close-popup @click="editActions.cut">
            <q-item-section>剪切</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="editActions.copy">
            <q-item-section>复制</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="editActions.paste">
            <q-item-section>粘贴</q-item-section>
          </q-item>
          <q-separator/>
          <q-item clickable v-close-popup @click="editActions.selectAll">
            <q-item-section>全选</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="editActions.deleteSelected">
            <q-item-section>删除</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </div>

    <div class="cursor-pointer non-selectable menu-section">
      对象
      <q-menu>
        <q-list dense style="min-width: 180px">
          <q-item
            v-for="(module, type) in ModuleConfig"
            :key="type"
            clickable
            v-close-popup
            @click="addModule(type)"
          >
            <q-item-section avatar style="min-width: 0">
              <img
                :src="`/images/modules/${type}.png`"
                :alt="module.name"
                style="width: 16px; height: 16px;"
              >
            </q-item-section>
            <q-item-section>
              <q-item-label>添加{{ module.name }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-separator/>
          <q-item
            v-for="(connection, type) in ConnectionConfig"
            :key="type"
            clickable
            v-close-popup
            @click="setConnectionMode(type)"
            :class="{ 'bg-blue-5 text-white': editorStore.connectionMode === type }"
          >
            <q-item-section avatar style="min-width: 0">
              <q-icon :name="connection.icon" style="width: 16px; height: 16px;"/>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ connection.name }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </div>

    <div class="cursor-pointer non-selectable menu-section">
      接口
      <q-menu>
        <q-list dense style="min-width: 180px">
          <q-item clickable v-close-popup @click="interfaceActions.openDeviceAttributes">
            <q-item-section>设备模型属性</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="interfaceActions.openAttributeRedirect">
            <q-item-section>属性重定向</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </div>

    <q-space/>

  </q-bar>
</template>

<script setup>
import {inject} from 'vue'
import {useEditorStore} from 'stores/editor'
import {ConnectionConfig, ModuleConfig} from 'src/utils/modules.js'

const editorStore = useEditorStore()
const editorRef = inject('editorRef')

// 文件操作
const fileActions = {
  newModel: () => {
    if (editorRef.value) {
      editorRef.value.newModel()
    }
  },
  openModel: () => {
    // TODO: 实现打开模型
    console.log('打开模型')
  },
  saveModel: () => {
    if (editorRef.value) {
      try {
        const devMData = editorRef.value.exportToDevM()
        if (devMData) {
          const jsonString = JSON.stringify(devMData, null, 2)
          const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8' })
          const url = URL.createObjectURL(blob)

          const a = document.createElement('a')
          a.href = url
          a.download = "network_model.dev.m"
          a.style.display = 'none'

          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)

          setTimeout(() => {
            URL.revokeObjectURL(url)
          }, 100)

          editorStore.hasUnsavedChanges = false
          console.log('模型保存成功')
        }
      } catch (error) {
        console.error('保存模型失败:', error)
      }
    }
  },
  saveAsModel: () => {
    // 与保存模型相同，可以后续扩展为选择文件名
    fileActions.saveModel()
  }
}

// 编辑操作
const editActions = {
  undo: () => {
    if (editorRef.value) {
      editorRef.value.undo()
    }
  },
  redo: () => {
    if (editorRef.value) {
      editorRef.value.redo()
    }
  },
  cut: () => {
    if (editorRef.value) {
      editorRef.value.cut()
    }
  },
  copy: () => {
    if (editorRef.value) {
      editorRef.value.copy()
    }
  },
  paste: () => {
    if (editorRef.value) {
      editorRef.value.paste()
    }
  },
  selectAll: () => {
    if (editorRef.value) {
      editorRef.value.selectAll()
    }
  },
  deleteSelected: () => {
    if (editorRef.value) {
      editorRef.value.deleteSelected()
    }
  }
}

// 添加模块
const addModule = (moduleType) => {
  if (editorRef.value) {
    editorRef.value.addModule(moduleType)
  }
}

// 设置连接模式
const setConnectionMode = (mode) => {
  editorStore.connectionMode = mode;
}

// 接口操作
const interfaceActions = {
  openDeviceAttributes: () => {
    editorStore.deviceAttributeDialog.visible = true
  },
  openAttributeRedirect: () => {
    editorStore.attributeRedirectDialog.visible = true
  }
}
</script>

<style lang="scss" scoped>
.menu-section {
  padding: 8px 8px;
}

.status-text {
  font-size: 12px;
  color: #ffffff;
  padding: 0 8px;
}

</style>
