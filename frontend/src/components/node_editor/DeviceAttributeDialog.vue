<template>
  <q-dialog
    v-model="editorStore.deviceAttributeDialog.visible"
    persistent
    maximized
  >
    <q-card class="device-attribute-dialog">
      <q-card-section class="row dialog-header">
        <span>设备模型属性</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-content">
        <AttributeTable
          :attributes="attributes"
          @update:attributes="updateAttributes"
          @edit-children="editChildren"
        />
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="saveAndClose"
        />
      </q-card-actions>
    </q-card>

    <!-- 子属性编辑器 -->
    <ChildrenEditor
      ref="childrenEditorRef"
      v-model="childrenDialog.visible"
      @save="onChildrenSave"
    />
  </q-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useEditorStore } from 'stores/editor'
import AttributeTable from './AttributeTable.vue'
import ChildrenEditor from './ChildrenEditor.vue'

const editorStore = useEditorStore()

// 子属性编辑器引用
const childrenEditorRef = ref(null)

// 子属性编辑对话框状态
const childrenDialog = ref({
  visible: false
})

// 属性数据 - 使用本地副本避免直接修改store
const attributes = ref([])

// 原始数据备份，用于取消时恢复
const originalAttributes = ref([])

// 监听对话框打开，初始化数据
watch(() => editorStore.deviceAttributeDialog.visible, (visible) => {
  if (visible) {
    // 深拷贝原始数据
    originalAttributes.value = JSON.parse(JSON.stringify(editorStore.deviceAttributeDialog.attributes || []))
    attributes.value = JSON.parse(JSON.stringify(editorStore.deviceAttributeDialog.attributes || []))
  }
})

// 更新属性列表
const updateAttributes = (newAttributes) => {
  attributes.value = newAttributes
}

// 编辑子属性
const editChildren = (attribute) => {
  if (childrenEditorRef.value) {
    childrenEditorRef.value.open(attribute)
  }
}

// 子属性保存回调
const onChildrenSave = (attribute) => {
  // 属性已经在 ChildrenEditor 中更新了，这里不需要额外处理
  console.log('子属性已保存:', attribute)
}

// 关闭对话框（取消）
const closeDialog = () => {
  // 恢复原始数据
  editorStore.deviceAttributeDialog.attributes = [...originalAttributes.value]
  editorStore.deviceAttributeDialog.visible = false
  
  // 清空本地数据
  attributes.value = []
  originalAttributes.value = []
}

// 保存并关闭
const saveAndClose = () => {
  // 保存修改到store
  editorStore.deviceAttributeDialog.attributes = [...attributes.value]
  editorStore.deviceAttributeDialog.visible = false
  
  // 清空本地数据
  attributes.value = []
  originalAttributes.value = []
}
</script>

<style scoped>
.device-attribute-dialog {
  width: 90vw;
  height: 90vh;
}

.dialog-header {
  background-color: #f5f5f5;
  padding: 16px;
  font-weight: 500;
}

.dialog-content {
  flex: 1;
  padding: 16px;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}
</style>
