<template>
  <q-dialog
    v-model="editorStore.deviceAttributeDialog.visible"
    persistent
    maximized
  >
    <q-card class="device-attribute-dialog">
      <q-card-section class="row dialog-header">
        <span>设备模型属性</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-content">
        <div class="attribute-table-container">
          <div class="table-toolbar">
            <q-btn
              color="primary"
              icon="add"
              label="添加属性"
              @click="addAttribute"
            />
            <q-btn
              color="negative"
              icon="delete"
              label="删除选中"
              :disable="selectedRows.length === 0"
              @click="deleteSelected"
            />
          </div>

          <q-table
            :rows="attributes"
            :columns="columns"
            row-key="name"
            selection="multiple"
            v-model:selected="selectedRows"
            :pagination="{ rowsPerPage: 0 }"
            class="attribute-table"
          >
            <template v-slot:body-cell-name="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.name"
                  dense
                  borderless
                  @blur="validateAttributeName(props.row)"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-label="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.label"
                  dense
                  borderless
                />
              </q-td>
            </template>

            <template v-slot:body-cell-type="props">
              <q-td :props="props">
                <q-select
                  v-model="props.row.type"
                  :options="typeOptions"
                  dense
                  borderless
                  emit-value
                  map-options
                />
              </q-td>
            </template>

            <template v-slot:body-cell-defaultValue="props">
              <q-td :props="props">
                <q-input
                  v-if="props.row.type !== 'boolean' && props.row.type !== 'select'"
                  v-model="props.row.defaultValue"
                  dense
                  borderless
                  :type="props.row.type === 'number' ? 'number' : 'text'"
                />
                <q-checkbox
                  v-else-if="props.row.type === 'boolean'"
                  v-model="props.row.defaultValue"
                />
                <q-select
                  v-else-if="props.row.type === 'select'"
                  v-model="props.row.defaultValue"
                  :options="getSelectOptions(props.row)"
                  dense
                  borderless
                  emit-value
                  map-options
                />
              </q-td>
            </template>

            <template v-slot:body-cell-desc="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.desc"
                  dense
                  borderless
                />
              </q-td>
            </template>

            <template v-slot:body-cell-unit="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.unit"
                  dense
                  borderless
                />
              </q-td>
            </template>

            <template v-slot:body-cell-symbolMap="props">
              <q-td :props="props">
                <q-btn
                  v-if="props.row.type === 'select'"
                  flat
                  dense
                  icon="edit"
                  @click="editSymbolMap(props.row)"
                />
                <span v-else>-</span>
              </q-td>
            </template>
          </q-table>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="saveAndClose"
        />
      </q-card-actions>
    </q-card>

    <!-- 符号映射编辑对话框 -->
    <q-dialog v-model="symbolMapDialog.visible" persistent>
      <q-card style="min-width: 400px">
        <q-card-section class="row dialog-header">
          <span>编辑选项</span>
          <q-space />
          <q-btn
            flat
            dense
            round
            icon="close"
            @click="symbolMapDialog.visible = false"
          />
        </q-card-section>

        <q-card-section>
          <div class="symbol-map-editor">
            <div
              v-for="(option, index) in symbolMapDialog.options"
              :key="index"
              class="option-row"
            >
              <q-input
                v-model="option.value"
                label="值"
                dense
                class="option-input"
              />
              <q-input
                v-model="option.label"
                label="显示标签"
                dense
                class="option-input"
              />
              <q-btn
                flat
                dense
                round
                icon="delete"
                @click="removeOption(index)"
              />
            </div>
            <q-btn
              flat
              icon="add"
              label="添加选项"
              @click="addOption"
            />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            flat
            label="取消"
            @click="symbolMapDialog.visible = false"
          />
          <q-btn
            unelevated
            label="确定"
            color="primary"
            @click="saveSymbolMap"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useEditorStore } from 'stores/editor'

const editorStore = useEditorStore()

// 表格列定义
const columns = [
  {
    name: 'name',
    label: '属性名',
    field: 'name',
    required: true,
    align: 'left',
    sortable: true
  },
  {
    name: 'label',
    label: '显示标签',
    field: 'label',
    align: 'left'
  },
  {
    name: 'type',
    label: '类型',
    field: 'type',
    align: 'left'
  },
  {
    name: 'defaultValue',
    label: '默认值',
    field: 'defaultValue',
    align: 'left'
  },
  {
    name: 'desc',
    label: '描述',
    field: 'desc',
    align: 'left'
  },
  {
    name: 'unit',
    label: '单位',
    field: 'unit',
    align: 'left'
  },
  {
    name: 'symbolMap',
    label: '选项',
    field: 'symbolMap',
    align: 'center'
  }
]

// 类型选项
const typeOptions = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: '选择', value: 'select' }
]

// 属性数据
const attributes = computed({
  get: () => editorStore.deviceAttributeDialog.attributes,
  set: (value) => {
    editorStore.deviceAttributeDialog.attributes = value
  }
})

// 选中的行
const selectedRows = ref([])

// 符号映射编辑对话框
const symbolMapDialog = ref({
  visible: false,
  currentAttribute: null,
  options: []
})

// 添加属性
const addAttribute = () => {
  const newAttribute = {
    name: `attribute_${attributes.value.length + 1}`,
    label: '新属性',
    type: 'string',
    desc: '',
    unit: '',
    defaultValue: '',
    symbolMap: [],
    allowOtherValues: true
  }
  attributes.value.push(newAttribute)
}

// 删除选中的属性
const deleteSelected = () => {
  selectedRows.value.forEach(row => {
    const index = attributes.value.findIndex(attr => attr.name === row.name)
    if (index > -1) {
      attributes.value.splice(index, 1)
    }
  })
  selectedRows.value = []
}

// 验证属性名
const validateAttributeName = (attribute) => {
  // 检查属性名是否重复
  const duplicates = attributes.value.filter(attr => attr.name === attribute.name)
  if (duplicates.length > 1) {
    // 如果重复，添加后缀
    let counter = 1
    let newName = `${attribute.name}_${counter}`
    while (attributes.value.some(attr => attr.name === newName)) {
      counter++
      newName = `${attribute.name}_${counter}`
    }
    attribute.name = newName
  }
}

// 获取选择类型的选项
const getSelectOptions = (attribute) => {
  return attribute.symbolMap?.map(item => ({
    label: item.label,
    value: item.value
  })) || []
}

// 编辑符号映射
const editSymbolMap = (attribute) => {
  symbolMapDialog.value.currentAttribute = attribute
  symbolMapDialog.value.options = [...(attribute.symbolMap || [])]
  symbolMapDialog.value.visible = true
}

// 添加选项
const addOption = () => {
  symbolMapDialog.value.options.push({
    value: '',
    label: ''
  })
}

// 删除选项
const removeOption = (index) => {
  symbolMapDialog.value.options.splice(index, 1)
}

// 保存符号映射
const saveSymbolMap = () => {
  if (symbolMapDialog.value.currentAttribute) {
    symbolMapDialog.value.currentAttribute.symbolMap = [...symbolMapDialog.value.options]
  }
  symbolMapDialog.value.visible = false
}

// 关闭对话框
const closeDialog = () => {
  editorStore.deviceAttributeDialog.visible = false
  selectedRows.value = []
}

// 保存并关闭
const saveAndClose = () => {
  // 这里可以添加保存逻辑
  closeDialog()
}
</script>

<style scoped>
.device-attribute-dialog {
  width: 90vw;
  height: 90vh;
}

.dialog-header {
  background-color: #f5f5f5;
  padding: 16px;
  font-weight: 500;
}

.dialog-content {
  flex: 1;
  padding: 16px;
}

.attribute-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.attribute-table {
  flex: 1;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.symbol-map-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.option-input {
  flex: 1;
}
</style>
