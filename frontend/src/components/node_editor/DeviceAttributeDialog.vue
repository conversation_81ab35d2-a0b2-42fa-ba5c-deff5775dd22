<template>
  <q-dialog
    v-model="editorStore.deviceAttributeDialog.visible"
    persistent
    maximized
  >
    <q-card class="device-attribute-dialog">
      <q-card-section class="row dialog-header">
        <span>设备模型属性</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-content">
        <div class="attribute-table-container">
          <div class="table-toolbar">
            <q-btn
              color="primary"
              icon="add"
              label="添加属性"
              @click="addAttribute"
            />
            <q-btn
              color="negative"
              icon="delete"
              label="删除选中"
              :disable="selectedRows.length === 0"
              @click="deleteSelected"
            />
          </div>

          <q-table
            :rows="attributes"
            :columns="columns"
            row-key="name"
            selection="multiple"
            v-model:selected="selectedRows"
            :pagination="{ rowsPerPage: 0 }"
            class="attribute-table"
          >
            <template v-slot:body-cell-name="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.name"
                  dense
                  borderless
                  @blur="validateAttributeName(props.row)"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-label="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.label"
                  dense
                  borderless
                />
              </q-td>
            </template>

            <template v-slot:body-cell-type="props">
              <q-td :props="props">
                <q-select
                  v-model="props.row.type"
                  :options="typeOptions"
                  dense
                  options-dense
                  borderless
                  emit-value
                  map-options
                />
              </q-td>
            </template>

            <template v-slot:body-cell-defaultValue="props">
              <q-td :props="props">
                <q-input
                  v-if="['string'].includes(props.row.type)"
                  v-model="props.row.defaultValue"
                  dense
                  outlined
                  type="text"
                />
                <q-input
                  v-else-if="['int', 'float'].includes(props.row.type)"
                  v-model="props.row.defaultValue"
                  dense
                  outlined
                  type="number"
                  :step="props.row.type === 'float' ? 'any' : '1'"
                />
                <q-toggle
                  v-else-if="props.row.type === 'bool'"
                  v-model="props.row.defaultValue"
                />
                <q-select
                  v-else-if="props.row.type === 'option'"
                  v-model="props.row.defaultValue"
                  :options="getSelectOptions(props.row)"
                  dense
                  options-dense
                  outlined
                  emit-value
                  map-options
                />
                <q-input
                  v-else-if="['object', 'array'].includes(props.row.type)"
                  v-model="props.row.defaultValue"
                  dense
                  outlined
                  type="text"
                  placeholder="JSON格式"
                  readonly
                />
                <q-input
                  v-else
                  v-model="props.row.defaultValue"
                  dense
                  outlined
                  type="text"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-desc="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.desc"
                  dense
                  outlined
                />
              </q-td>
            </template>

            <template v-slot:body-cell-unit="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.unit"
                  dense
                  outlined
                />
              </q-td>
            </template>

            <template v-slot:body-cell-symbolMap="props">
              <q-td :props="props">
                <q-btn
                  flat
                  dense
                  icon="edit"
                  @click="editSymbolMap(props.row)"
                  :label="props.row.symbolMap && props.row.symbolMap.length > 0 ? `${props.row.symbolMap.length}项` : '编辑'"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-children="props">
              <q-td :props="props">
                <q-btn
                  v-if="['object', 'array'].includes(props.row.type)"
                  flat
                  dense
                  icon="edit"
                  @click="editChildren(props.row)"
                  :label="props.row.children && props.row.children.length > 0 ? `${props.row.children.length}项` : '编辑'"
                />
                <span v-else>-</span>
              </q-td>
            </template>
          </q-table>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="saveAndClose"
        />
      </q-card-actions>
    </q-card>

    <!-- 符号映射编辑对话框 -->
    <q-dialog v-model="symbolMapDialog.visible" persistent>
      <q-card style="min-width: 400px">
        <q-card-section class="row dialog-header">
          <span>编辑选项</span>
          <q-space />
          <q-btn
            flat
            dense
            round
            icon="close"
            @click="symbolMapDialog.visible = false"
          />
        </q-card-section>

        <q-card-section>
          <div class="symbol-map-editor">
            <div
              v-for="(option, index) in symbolMapDialog.options"
              :key="index"
              class="option-row"
            >
              <q-input
                v-model="option.key"
                label="显示标签"
                dense
                class="option-input"
              />
              <q-input
                v-model="option.value"
                label="值"
                dense
                class="option-input"
              />
              <q-btn
                flat
                dense
                round
                icon="delete"
                @click="removeOption(index)"
              />
            </div>
            <q-btn
              flat
              icon="add"
              label="添加选项"
              @click="addOption"
            />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            flat
            label="取消"
            @click="cancelSymbolMap"
          />
          <q-btn
            unelevated
            label="确定"
            color="primary"
            @click="saveSymbolMap"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 子属性编辑对话框 -->
    <q-dialog v-model="childrenDialog.visible" persistent maximized>
      <q-card class="children-dialog">
        <q-card-section class="row dialog-header">
          <div class="column">
            <div class="row items-center">
              <q-btn
                v-if="childrenDialog.breadcrumb.length > 0"
                flat
                dense
                round
                icon="arrow_back"
                @click="goBackToParent"
                class="q-mr-sm"
              />
              <span>编辑子属性</span>
              <q-space />
              <q-btn
                flat
                dense
                round
                icon="close"
                @click="closeChildrenDialog"
              />
            </div>
            <!-- 面包屑导航 -->
            <div v-if="childrenDialog.breadcrumb.length > 0" class="breadcrumb">
              <q-breadcrumbs>
                <q-breadcrumbs-el
                  v-for="(crumb, index) in childrenDialog.breadcrumb"
                  :key="index"
                  :label="crumb.label"
                  @click="goToBreadcrumb(index)"
                  class="cursor-pointer"
                />
                <q-breadcrumbs-el
                  :label="childrenDialog.currentAttribute?.label || childrenDialog.currentAttribute?.name"
                />
              </q-breadcrumbs>
            </div>
          </div>
        </q-card-section>

        <q-card-section class="dialog-content">
          <div class="children-table-container">
            <div class="table-toolbar">
              <q-btn
                color="primary"
                icon="add"
                label="添加子属性"
                @click="addChildAttribute"
              />
              <q-btn
                color="negative"
                icon="delete"
                label="删除选中"
                :disable="selectedChildRows.length === 0"
                @click="deleteSelectedChildren"
              />
            </div>

            <q-table
              :rows="childrenDialog.children"
              :columns="childrenColumns"
              row-key="name"
              selection="multiple"
              v-model:selected="selectedChildRows"
              :pagination="{ rowsPerPage: 0 }"
              class="children-table"
            >
              <template v-slot:body-cell-name="props">
                <q-td :props="props">
                  <q-input
                    v-model="props.row.name"
                    dense
                    borderless
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-label="props">
                <q-td :props="props">
                  <q-input
                    v-model="props.row.label"
                    dense
                    borderless
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-type="props">
                <q-td :props="props">
                  <q-select
                    v-model="props.row.type"
                    :options="typeOptions"
                    dense
                    borderless
                    emit-value
                    map-options
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-defaultValue="props">
                <q-td :props="props">
                  <q-input
                    v-if="['string'].includes(props.row.type)"
                    v-model="props.row.defaultValue"
                    dense
                    borderless
                    type="text"
                  />
                  <q-input
                    v-else-if="['int', 'float'].includes(props.row.type)"
                    v-model="props.row.defaultValue"
                    dense
                    borderless
                    type="number"
                    :step="props.row.type === 'float' ? 'any' : '1'"
                  />
                  <q-checkbox
                    v-else-if="props.row.type === 'bool'"
                    v-model="props.row.defaultValue"
                  />
                  <q-input
                    v-else
                    v-model="props.row.defaultValue"
                    dense
                    borderless
                    type="text"
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-desc="props">
                <q-td :props="props">
                  <q-input
                    v-model="props.row.desc"
                    dense
                    borderless
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-children="props">
                <q-td :props="props">
                  <q-btn
                    v-if="['object', 'array'].includes(props.row.type)"
                    flat
                    dense
                    icon="edit"
                    @click="editNestedChildren(props.row)"
                    :label="props.row.children && props.row.children.length > 0 ? `${props.row.children.length}项` : '编辑'"
                  />
                  <span v-else>-</span>
                </q-td>
              </template>
            </q-table>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            flat
            label="取消"
            @click="cancelChildren"
          />
          <q-btn
            unelevated
            label="确定"
            color="primary"
            @click="saveChildren"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useEditorStore } from 'stores/editor'

const editorStore = useEditorStore()

// 表格列定义
const columns = [
  {
    name: 'name',
    label: '属性名',
    field: 'name',
    required: true,
    align: 'left',
    sortable: true
  },
  {
    name: 'label',
    label: '显示名',
    field: 'label',
    align: 'left'
  },
  {
    name: 'type',
    label: '类型',
    field: 'type',
    align: 'left'
  },
  {
    name: 'defaultValue',
    label: '默认值',
    field: 'defaultValue',
    align: 'left'
  },
  {
    name: 'desc',
    label: '描述',
    field: 'desc',
    align: 'left'
  },
  {
    name: 'unit',
    label: '单位',
    field: 'unit',
    align: 'left'
  },
  {
    name: 'symbolMap',
    label: '常用值',
    field: 'symbolMap',
    align: 'center'
  },
  {
    name: 'children',
    label: '子属性',
    field: 'children',
    align: 'center'
  }
]

// 类型选项
const typeOptions = [
  { label: '字符串', value: 'string' },
  { label: '整数', value: 'int' },
  { label: '浮点数', value: 'float' },
  // { label: '选项', value: 'option' },
  { label: '布尔值', value: 'bool' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' }
]

// 属性数据 - 使用本地副本避免直接修改store
const attributes = ref([])

// 原始数据备份，用于取消时恢复
const originalAttributes = ref([])

// 选中的行
const selectedRows = ref([])

// 监听对话框打开，初始化数据
watch(() => editorStore.deviceAttributeDialog.visible, (visible) => {
  if (visible) {
    // 深拷贝原始数据
    originalAttributes.value = JSON.parse(JSON.stringify(editorStore.deviceAttributeDialog.attributes || []))
    attributes.value = JSON.parse(JSON.stringify(editorStore.deviceAttributeDialog.attributes || []))
    selectedRows.value = []
  }
})

// 符号映射编辑对话框
const symbolMapDialog = ref({
  visible: false,
  currentAttribute: null,
  options: [],
  originalOptions: []
})

// 子属性编辑对话框 - 支持递归编辑
const childrenDialog = ref({
  visible: false,
  currentAttribute: null,
  children: [],
  originalChildren: [],
  breadcrumb: [], // 面包屑导航，记录编辑路径
  dialogStack: [] // 对话框堆栈，支持多层嵌套
})

// 子属性表格列定义
const childrenColumns = [
  {
    name: 'name',
    label: '属性名',
    field: 'name',
    required: true,
    align: 'left',
    sortable: true
  },
  {
    name: 'label',
    label: '显示标签',
    field: 'label',
    align: 'left'
  },
  {
    name: 'type',
    label: '类型',
    field: 'type',
    align: 'left'
  },
  {
    name: 'defaultValue',
    label: '默认值',
    field: 'defaultValue',
    align: 'left'
  },
  {
    name: 'desc',
    label: '描述',
    field: 'desc',
    align: 'left'
  },
  {
    name: 'children',
    label: '子属性',
    field: 'children',
    align: 'center'
  }
]

// 选中的子属性行
const selectedChildRows = ref([])

// 添加属性
const addAttribute = () => {
  const newAttribute = {
    name: `attribute_${attributes.value.length + 1}`,
    label: '新属性',
    type: 'string',
    desc: '',
    unit: '',
    defaultValue: '',
    symbolMap: [],
    allowOtherValues: true,
    children: []
  }
  attributes.value.push(newAttribute)
}

// 删除选中的属性
const deleteSelected = () => {
  selectedRows.value.forEach(row => {
    const index = attributes.value.findIndex(attr => attr.name === row.name)
    if (index > -1) {
      attributes.value.splice(index, 1)
    }
  })
  selectedRows.value = []
}

// 验证属性名
const validateAttributeName = (attribute) => {
  // 检查属性名是否重复
  const duplicates = attributes.value.filter(attr => attr.name === attribute.name)
  if (duplicates.length > 1) {
    // 如果重复，添加后缀
    let counter = 1
    let newName = `${attribute.name}_${counter}`
    while (attributes.value.some(attr => attr.name === newName)) {
      counter++
      newName = `${attribute.name}_${counter}`
    }
    attribute.name = newName
  }
}

// 获取选择类型的选项
const getSelectOptions = (attribute) => {
  return attribute.symbolMap?.map(item => ({
    label: item.label || item.key,
    value: item.value
  })) || []
}

// 编辑符号映射
const editSymbolMap = (attribute) => {
  symbolMapDialog.value.currentAttribute = attribute
  symbolMapDialog.value.originalOptions = JSON.parse(JSON.stringify(attribute.symbolMap || []))
  symbolMapDialog.value.options = JSON.parse(JSON.stringify(attribute.symbolMap || []))
  symbolMapDialog.value.visible = true
}

// 添加选项
const addOption = () => {
  symbolMapDialog.value.options.push({
    key: '',
    value: ''
  })
}

// 删除选项
const removeOption = (index) => {
  symbolMapDialog.value.options.splice(index, 1)
}

// 取消符号映射编辑
const cancelSymbolMap = () => {
  symbolMapDialog.value.visible = false
  symbolMapDialog.value.options = []
  symbolMapDialog.value.originalOptions = []
  symbolMapDialog.value.currentAttribute = null
}

// 保存符号映射
const saveSymbolMap = () => {
  if (symbolMapDialog.value.currentAttribute) {
    symbolMapDialog.value.currentAttribute.symbolMap = [...symbolMapDialog.value.options]
  }
  symbolMapDialog.value.visible = false
  symbolMapDialog.value.options = []
  symbolMapDialog.value.originalOptions = []
  symbolMapDialog.value.currentAttribute = null
}

// 编辑子属性（顶层）
const editChildren = (attribute) => {
  childrenDialog.value.currentAttribute = attribute
  childrenDialog.value.originalChildren = JSON.parse(JSON.stringify(attribute.children || []))
  childrenDialog.value.children = JSON.parse(JSON.stringify(attribute.children || []))
  childrenDialog.value.breadcrumb = []
  childrenDialog.value.dialogStack = []
  childrenDialog.value.visible = true
}

// 编辑嵌套子属性
const editNestedChildren = (attribute) => {
  // 保存当前状态到堆栈
  childrenDialog.value.dialogStack.push({
    currentAttribute: childrenDialog.value.currentAttribute,
    children: [...childrenDialog.value.children],
    originalChildren: [...childrenDialog.value.originalChildren]
  })

  // 添加到面包屑
  childrenDialog.value.breadcrumb.push({
    label: childrenDialog.value.currentAttribute?.label || childrenDialog.value.currentAttribute?.name,
    attribute: childrenDialog.value.currentAttribute
  })

  // 切换到新的子属性编辑
  childrenDialog.value.currentAttribute = attribute
  childrenDialog.value.originalChildren = JSON.parse(JSON.stringify(attribute.children || []))
  childrenDialog.value.children = JSON.parse(JSON.stringify(attribute.children || []))
  selectedChildRows.value = []
}

// 返回上一级
const goBackToParent = () => {
  if (childrenDialog.value.dialogStack.length > 0) {
    // 保存当前修改到当前属性
    if (childrenDialog.value.currentAttribute) {
      childrenDialog.value.currentAttribute.children = [...childrenDialog.value.children]
    }

    // 恢复上一级状态
    const parentState = childrenDialog.value.dialogStack.pop()
    childrenDialog.value.currentAttribute = parentState.currentAttribute
    childrenDialog.value.children = parentState.children
    childrenDialog.value.originalChildren = parentState.originalChildren

    // 移除面包屑最后一项
    childrenDialog.value.breadcrumb.pop()
    selectedChildRows.value = []
  }
}

// 跳转到面包屑指定位置
const goToBreadcrumb = (index) => {
  // 保存当前修改
  if (childrenDialog.value.currentAttribute) {
    childrenDialog.value.currentAttribute.children = [...childrenDialog.value.children]
  }

  // 计算需要返回的层数
  const stepsBack = childrenDialog.value.breadcrumb.length - index

  // 逐级返回
  for (let i = 0; i < stepsBack; i++) {
    if (childrenDialog.value.dialogStack.length > 0) {
      const parentState = childrenDialog.value.dialogStack.pop()
      childrenDialog.value.currentAttribute = parentState.currentAttribute
      childrenDialog.value.children = parentState.children
      childrenDialog.value.originalChildren = parentState.originalChildren
    }
  }

  // 更新面包屑
  childrenDialog.value.breadcrumb = childrenDialog.value.breadcrumb.slice(0, index)
  selectedChildRows.value = []
}

// 添加子属性
const addChildAttribute = () => {
  const newChild = {
    name: `child_${childrenDialog.value.children.length + 1}`,
    label: '新子属性',
    type: 'string',
    desc: '',
    defaultValue: '',
    children: [],
    symbolMap: [],
    allowOtherValues: true
  }
  childrenDialog.value.children.push(newChild)
}

// 删除选中的子属性
const deleteSelectedChildren = () => {
  selectedChildRows.value.forEach(row => {
    const index = childrenDialog.value.children.findIndex(child => child.name === row.name)
    if (index > -1) {
      childrenDialog.value.children.splice(index, 1)
    }
  })
  selectedChildRows.value = []
}

// 关闭子属性对话框
const closeChildrenDialog = () => {
  childrenDialog.value.visible = false
}

// 取消子属性编辑
const cancelChildren = () => {
  // 如果在嵌套编辑中，需要恢复所有层级的原始状态
  while (childrenDialog.value.dialogStack.length > 0) {
    const parentState = childrenDialog.value.dialogStack.pop()
    childrenDialog.value.currentAttribute = parentState.currentAttribute
    childrenDialog.value.children = parentState.children
    childrenDialog.value.originalChildren = parentState.originalChildren
  }

  childrenDialog.value.visible = false
  childrenDialog.value.children = []
  childrenDialog.value.originalChildren = []
  childrenDialog.value.currentAttribute = null
  childrenDialog.value.breadcrumb = []
  childrenDialog.value.dialogStack = []
  selectedChildRows.value = []
}

// 保存子属性
const saveChildren = () => {
  // 保存当前层级的修改
  if (childrenDialog.value.currentAttribute) {
    childrenDialog.value.currentAttribute.children = [...childrenDialog.value.children]
  }

  // 逐级保存所有嵌套修改
  while (childrenDialog.value.dialogStack.length > 0) {
    const parentState = childrenDialog.value.dialogStack.pop()
    if (parentState.currentAttribute) {
      // 在父级中找到对应的子属性并更新
      const childIndex = parentState.children.findIndex(child =>
        child.name === childrenDialog.value.currentAttribute.name
      )
      if (childIndex > -1) {
        parentState.children[childIndex] = { ...childrenDialog.value.currentAttribute }
      }
    }

    childrenDialog.value.currentAttribute = parentState.currentAttribute
    childrenDialog.value.children = parentState.children
    childrenDialog.value.originalChildren = parentState.originalChildren
  }

  // 最终保存到顶层属性
  if (childrenDialog.value.currentAttribute) {
    childrenDialog.value.currentAttribute.children = [...childrenDialog.value.children]
  }

  childrenDialog.value.visible = false
  childrenDialog.value.children = []
  childrenDialog.value.originalChildren = []
  childrenDialog.value.currentAttribute = null
  childrenDialog.value.breadcrumb = []
  childrenDialog.value.dialogStack = []
  selectedChildRows.value = []
}

// 关闭对话框（取消）
const closeDialog = () => {
  // 恢复原始数据
  editorStore.deviceAttributeDialog.attributes = [...originalAttributes.value]
  editorStore.deviceAttributeDialog.visible = false
  selectedRows.value = []

  // 清空本地数据
  attributes.value = []
  originalAttributes.value = []
}

// 保存并关闭
const saveAndClose = () => {
  // 保存修改到store
  editorStore.deviceAttributeDialog.attributes = [...attributes.value]
  editorStore.deviceAttributeDialog.visible = false
  selectedRows.value = []

  // 清空本地数据
  attributes.value = []
  originalAttributes.value = []
}
</script>

<style scoped>
.device-attribute-dialog {
  width: 90vw;
  height: 90vh;
}

.dialog-header {
  background-color: #f5f5f5;
  padding: 16px;
  font-weight: 500;
}

.dialog-content {
  flex: 1;
  padding: 16px;
}

.attribute-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.attribute-table {
  flex: 1;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.symbol-map-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.option-input {
  flex: 1;
}

.children-dialog {
  width: 90vw;
  height: 90vh;
}

.children-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.children-table {
  flex: 1;
}

.breadcrumb {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.breadcrumb .q-breadcrumbs-el {
  cursor: pointer;
}

.breadcrumb .q-breadcrumbs-el:hover {
  color: #1976d2;
}
</style>
