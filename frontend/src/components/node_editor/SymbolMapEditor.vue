<template>
  <q-dialog v-model="visible" persistent>
    <q-card style="min-width: 400px">
      <q-card-section class="row dialog-header">
        <span>编辑常用值</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="cancel"
        />
      </q-card-section>

      <q-card-section>
        <div class="symbol-map-editor">
          <div
            v-for="(option, index) in localOptions"
            :key="index"
            class="option-row"
          >
            <q-input
              v-model="option.key"
              label="显示标签"
              dense
              class="option-input"
            />
            <q-input
              v-model="option.value"
              label="值"
              dense
              class="option-input"
            />
            <q-btn
              flat
              dense
              round
              icon="delete"
              @click="removeOption(index)"
            />
          </div>
          <q-btn
            flat
            icon="add"
            label="添加选项"
            @click="addOption"
          />
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          label="取消"
          @click="cancel"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="save"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  symbolMap: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const visible = ref(false)
const localOptions = ref([])
const originalOptions = ref([])

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
  if (newValue) {
    // 深拷贝原始数据
    originalOptions.value = JSON.parse(JSON.stringify(props.symbolMap || []))
    localOptions.value = JSON.parse(JSON.stringify(props.symbolMap || []))
  }
})

// 监听 visible 变化，同步到父组件
watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 添加选项
const addOption = () => {
  localOptions.value.push({
    key: '',
    value: ''
  })
}

// 删除选项
const removeOption = (index) => {
  localOptions.value.splice(index, 1)
}

// 取消编辑
const cancel = () => {
  visible.value = false
  localOptions.value = []
  originalOptions.value = []
}

// 保存编辑
const save = () => {
  emit('save', [...localOptions.value])
  visible.value = false
  localOptions.value = []
  originalOptions.value = []
}
</script>

<style scoped>
.dialog-header {
  background-color: #f5f5f5;
  padding: 16px;
  font-weight: 500;
}

.symbol-map-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.option-input {
  flex: 1;
}
</style>
