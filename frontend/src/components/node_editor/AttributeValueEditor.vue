<template>
  <div class="attribute-value-editor">
    <!-- 字符串类型 -->
    <q-input
      v-if="attribute.type === 'string'"
      v-model="modelValue"
      dense
      borderless
      type="text"
      @update:model-value="$emit('update:modelValue', $event)"
    />
    
    <!-- 整数类型 -->
    <q-input
      v-else-if="attribute.type === 'int'"
      v-model.number="modelValue"
      dense
      borderless
      type="number"
      step="1"
      @update:model-value="$emit('update:modelValue', $event)"
    />
    
    <!-- 浮点数类型 -->
    <q-input
      v-else-if="attribute.type === 'float'"
      v-model.number="modelValue"
      dense
      borderless
      type="number"
      step="any"
      @update:model-value="$emit('update:modelValue', $event)"
    />
    
    <!-- 布尔值类型 -->
    <q-checkbox
      v-else-if="attribute.type === 'bool'"
      v-model="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
    />
    
    <!-- 选项类型 -->
    <q-select
      v-else-if="attribute.type === 'option'"
      v-model="modelValue"
      :options="selectOptions"
      dense
      borderless
      emit-value
      map-options
      @update:model-value="$emit('update:modelValue', $event)"
    />
    
    <!-- 对象和数组类型 -->
    <q-input
      v-else-if="['object', 'array'].includes(attribute.type)"
      v-model="modelValue"
      dense
      borderless
      type="text"
      placeholder="JSON格式"
      readonly
      @update:model-value="$emit('update:modelValue', $event)"
    />
    
    <!-- 其他类型 -->
    <q-input
      v-else
      v-model="modelValue"
      dense
      borderless
      type="text"
      @update:model-value="$emit('update:modelValue', $event)"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, Object, Array],
    default: ''
  },
  attribute: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

// 计算选项列表
const selectOptions = computed(() => {
  return props.attribute.symbolMap?.map(item => ({
    label: item.label || item.key,
    value: item.value
  })) || []
})

// 本地值，用于双向绑定
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style scoped>
.attribute-value-editor {
  width: 100%;
}
</style>
