<template>
  <q-dialog v-model="visible" persistent maximized>
    <q-card class="children-dialog">
      <q-card-section class="row dialog-header">
        <div class="column">
          <div class="row items-center">
            <q-btn
              v-if="breadcrumb.length > 0"
              flat
              dense
              round
              icon="arrow_back"
              @click="goBackToParent"
              class="q-mr-sm"
            />
            <span>编辑子属性</span>
            <q-space />
            <q-btn
              flat
              dense
              round
              icon="close"
              @click="close"
            />
          </div>
          <!-- 面包屑导航 -->
          <div v-if="breadcrumb.length > 0" class="breadcrumb">
            <q-breadcrumbs>
              <q-breadcrumbs-el
                v-for="(crumb, index) in breadcrumb"
                :key="index"
                :label="crumb.label"
                @click="goToBreadcrumb(index)"
                class="cursor-pointer"
              />
              <q-breadcrumbs-el
                :label="currentAttribute?.label || currentAttribute?.name"
              />
            </q-breadcrumbs>
          </div>
        </div>
      </q-card-section>

      <q-card-section class="dialog-content">
        <AttributeTable
          :attributes="children"
          @update:attributes="updateChildren"
          @edit-children="editNestedChildren"
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          label="取消"
          @click="cancel"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="save"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import AttributeTable from './AttributeTable.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const visible = ref(false)
const currentAttribute = ref(null)
const children = ref([])
const originalChildren = ref([])
const breadcrumb = ref([])
const dialogStack = ref([])

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
})

// 监听 visible 变化，同步到父组件
watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 打开编辑器
const open = (attribute) => {
  currentAttribute.value = attribute
  originalChildren.value = JSON.parse(JSON.stringify(attribute.children || []))
  children.value = JSON.parse(JSON.stringify(attribute.children || []))
  breadcrumb.value = []
  dialogStack.value = []
  visible.value = true
}

// 更新子属性
const updateChildren = (newChildren) => {
  children.value = newChildren
}

// 编辑嵌套子属性
const editNestedChildren = (attribute) => {
  // 保存当前状态到堆栈
  dialogStack.value.push({
    currentAttribute: currentAttribute.value,
    children: [...children.value],
    originalChildren: [...originalChildren.value]
  })
  
  // 添加到面包屑
  breadcrumb.value.push({
    label: currentAttribute.value?.label || currentAttribute.value?.name,
    attribute: currentAttribute.value
  })
  
  // 切换到新的子属性编辑
  currentAttribute.value = attribute
  originalChildren.value = JSON.parse(JSON.stringify(attribute.children || []))
  children.value = JSON.parse(JSON.stringify(attribute.children || []))
}

// 返回上一级
const goBackToParent = () => {
  if (dialogStack.value.length > 0) {
    // 保存当前修改到当前属性
    if (currentAttribute.value) {
      currentAttribute.value.children = [...children.value]
    }
    
    // 恢复上一级状态
    const parentState = dialogStack.value.pop()
    currentAttribute.value = parentState.currentAttribute
    children.value = parentState.children
    originalChildren.value = parentState.originalChildren
    
    // 移除面包屑最后一项
    breadcrumb.value.pop()
  }
}

// 跳转到面包屑指定位置
const goToBreadcrumb = (index) => {
  // 保存当前修改
  if (currentAttribute.value) {
    currentAttribute.value.children = [...children.value]
  }
  
  // 计算需要返回的层数
  const stepsBack = breadcrumb.value.length - index
  
  // 逐级返回
  for (let i = 0; i < stepsBack; i++) {
    if (dialogStack.value.length > 0) {
      const parentState = dialogStack.value.pop()
      currentAttribute.value = parentState.currentAttribute
      children.value = parentState.children
      originalChildren.value = parentState.originalChildren
    }
  }
  
  // 更新面包屑
  breadcrumb.value = breadcrumb.value.slice(0, index)
}

// 关闭对话框
const close = () => {
  visible.value = false
}

// 取消编辑
const cancel = () => {
  // 如果在嵌套编辑中，需要恢复所有层级的原始状态
  while (dialogStack.value.length > 0) {
    const parentState = dialogStack.value.pop()
    currentAttribute.value = parentState.currentAttribute
    children.value = parentState.children
    originalChildren.value = parentState.originalChildren
  }
  
  visible.value = false
  children.value = []
  originalChildren.value = []
  currentAttribute.value = null
  breadcrumb.value = []
  dialogStack.value = []
}

// 保存编辑
const save = () => {
  // 保存当前层级的修改
  if (currentAttribute.value) {
    currentAttribute.value.children = [...children.value]
  }
  
  // 逐级保存所有嵌套修改
  while (dialogStack.value.length > 0) {
    const parentState = dialogStack.value.pop()
    if (parentState.currentAttribute) {
      // 在父级中找到对应的子属性并更新
      const childIndex = parentState.children.findIndex(child => 
        child.name === currentAttribute.value.name
      )
      if (childIndex > -1) {
        parentState.children[childIndex] = { ...currentAttribute.value }
      }
    }
    
    currentAttribute.value = parentState.currentAttribute
    children.value = parentState.children
    originalChildren.value = parentState.originalChildren
  }
  
  // 最终保存到顶层属性
  if (currentAttribute.value) {
    currentAttribute.value.children = [...children.value]
    emit('save', currentAttribute.value)
  }
  
  visible.value = false
  children.value = []
  originalChildren.value = []
  currentAttribute.value = null
  breadcrumb.value = []
  dialogStack.value = []
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.children-dialog {
  width: 90vw;
  height: 90vh;
}

.dialog-header {
  background-color: #f5f5f5;
  padding: 16px;
  font-weight: 500;
}

.dialog-content {
  flex: 1;
  padding: 16px;
}

.breadcrumb {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.breadcrumb .q-breadcrumbs-el {
  cursor: pointer;
}

.breadcrumb .q-breadcrumbs-el:hover {
  color: #1976d2;
}
</style>
