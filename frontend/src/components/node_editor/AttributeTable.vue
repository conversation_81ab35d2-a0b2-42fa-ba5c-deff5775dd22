<template>
  <div class="attribute-table-container">
    <div class="table-toolbar">
      <q-btn
        color="primary"
        icon="add"
        label="添加属性"
        @click="addAttribute"
      />
      <q-btn
        color="negative"
        icon="delete"
        label="删除选中"
        :disable="selectedRows.length === 0"
        @click="deleteSelected"
      />
    </div>

    <q-table
      :rows="attributes"
      :columns="columns"
      row-key="name"
      selection="multiple"
      v-model:selected="selectedRows"
      :pagination="{ rowsPerPage: 0 }"
      class="attribute-table"
    >
      <template v-slot:body-cell-name="props">
        <q-td :props="props">
          <q-input
            v-model="props.row.name"
            dense
            borderless
            @blur="validateAttributeName(props.row)"
          />
        </q-td>
      </template>

      <template v-slot:body-cell-label="props">
        <q-td :props="props">
          <q-input
            v-model="props.row.label"
            dense
            borderless
          />
        </q-td>
      </template>

      <template v-slot:body-cell-type="props">
        <q-td :props="props">
          <q-select
            v-model="props.row.type"
            :options="typeOptions"
            dense
            borderless
            emit-value
            map-options
          />
        </q-td>
      </template>

      <template v-slot:body-cell-defaultValue="props">
        <q-td :props="props">
          <AttributeValueEditor
            v-model="props.row.defaultValue"
            :attribute="props.row"
          />
        </q-td>
      </template>

      <template v-slot:body-cell-desc="props">
        <q-td :props="props">
          <q-input
            v-model="props.row.desc"
            dense
            borderless
          />
        </q-td>
      </template>

      <template v-slot:body-cell-unit="props">
        <q-td :props="props">
          <q-input
            v-model="props.row.unit"
            dense
            borderless
          />
        </q-td>
      </template>

      <template v-slot:body-cell-symbolMap="props">
        <q-td :props="props">
          <q-btn
            flat
            dense
            icon="edit"
            @click="editSymbolMap(props.row)"
            :label="props.row.symbolMap && props.row.symbolMap.length > 0 ? `${props.row.symbolMap.length}项` : '编辑'"
          />
        </q-td>
      </template>

      <template v-slot:body-cell-children="props">
        <q-td :props="props">
          <q-btn
            v-if="['object', 'array'].includes(props.row.type)"
            flat
            dense
            icon="edit"
            @click="editChildren(props.row)"
            :label="props.row.children && props.row.children.length > 0 ? `${props.row.children.length}项` : '编辑'"
          />
          <span v-else>-</span>
        </q-td>
      </template>
    </q-table>

    <!-- 常用值编辑器 -->
    <SymbolMapEditor
      v-model="symbolMapDialog.visible"
      :symbol-map="symbolMapDialog.symbolMap"
      @save="saveSymbolMap"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AttributeValueEditor from './AttributeValueEditor.vue'
import SymbolMapEditor from './SymbolMapEditor.vue'

const props = defineProps({
  attributes: {
    type: Array,
    default: () => []
  },
  showChildrenColumn: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:attributes', 'edit-children'])

// 类型选项
const typeOptions = [
  { label: '字符串', value: 'string' },
  { label: '整数', value: 'int' },
  { label: '浮点数', value: 'float' },
  { label: '选项', value: 'option' },
  { label: '布尔值', value: 'bool' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' }
]

// 表格列定义
const baseColumns = [
  {
    name: 'name',
    label: '属性名',
    field: 'name',
    required: true,
    align: 'left',
    sortable: true
  },
  {
    name: 'label',
    label: '显示标签',
    field: 'label',
    align: 'left'
  },
  {
    name: 'type',
    label: '类型',
    field: 'type',
    align: 'left'
  },
  {
    name: 'defaultValue',
    label: '默认值',
    field: 'defaultValue',
    align: 'left'
  },
  {
    name: 'desc',
    label: '描述',
    field: 'desc',
    align: 'left'
  },
  {
    name: 'unit',
    label: '单位',
    field: 'unit',
    align: 'left'
  },
  {
    name: 'symbolMap',
    label: '常用值',
    field: 'symbolMap',
    align: 'center'
  }
]

const columns = ref([
  ...baseColumns,
  ...(props.showChildrenColumn ? [{
    name: 'children',
    label: '子属性',
    field: 'children',
    align: 'center'
  }] : [])
])

// 选中的行
const selectedRows = ref([])

// 常用值编辑对话框
const symbolMapDialog = ref({
  visible: false,
  currentAttribute: null,
  symbolMap: []
})

// 添加属性
const addAttribute = () => {
  const newAttribute = {
    name: `attribute_${props.attributes.length + 1}`,
    label: '新属性',
    type: 'string',
    desc: '',
    unit: '',
    defaultValue: '',
    symbolMap: [],
    allowOtherValues: true,
    children: []
  }
  const updatedAttributes = [...props.attributes, newAttribute]
  emit('update:attributes', updatedAttributes)
}

// 删除选中的属性
const deleteSelected = () => {
  const updatedAttributes = props.attributes.filter(attr => 
    !selectedRows.value.some(row => row.name === attr.name)
  )
  emit('update:attributes', updatedAttributes)
  selectedRows.value = []
}

// 验证属性名
const validateAttributeName = (attribute) => {
  const duplicates = props.attributes.filter(attr => attr.name === attribute.name)
  if (duplicates.length > 1) {
    let counter = 1
    let newName = `${attribute.name}_${counter}`
    while (props.attributes.some(attr => attr.name === newName)) {
      counter++
      newName = `${attribute.name}_${counter}`
    }
    attribute.name = newName
  }
}

// 编辑常用值
const editSymbolMap = (attribute) => {
  symbolMapDialog.value.currentAttribute = attribute
  symbolMapDialog.value.symbolMap = attribute.symbolMap || []
  symbolMapDialog.value.visible = true
}

// 保存常用值
const saveSymbolMap = (symbolMap) => {
  if (symbolMapDialog.value.currentAttribute) {
    symbolMapDialog.value.currentAttribute.symbolMap = symbolMap
  }
  symbolMapDialog.value.currentAttribute = null
  symbolMapDialog.value.symbolMap = []
}

// 编辑子属性
const editChildren = (attribute) => {
  emit('edit-children', attribute)
}
</script>

<style scoped>
.attribute-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.attribute-table {
  flex: 1;
}
</style>
