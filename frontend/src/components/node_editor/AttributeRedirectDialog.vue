<template>
  <q-dialog
    v-model="editorStore.attributeRedirectDialog.visible"
    persistent
    maximized
  >
    <q-card class="attribute-redirect-dialog">
      <q-card-section class="row dialog-header">
        <span>属性重定向</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-content">
        <div class="redirect-table-container">
          <div class="table-toolbar">
            <q-btn
              color="primary"
              icon="add"
              label="添加重定向"
              @click="addRedirect"
            />
            <q-btn
              color="negative"
              icon="delete"
              label="删除选中"
              :disable="selectedRows.length === 0"
              @click="deleteSelected"
            />
          </div>

          <q-table
            :rows="redirects"
            :columns="columns"
            row-key="id"
            selection="multiple"
            v-model:selected="selectedRows"
            :pagination="{ rowsPerPage: 0 }"
            class="redirect-table"
          >
            <template v-slot:body-cell-exposedName="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.exposedName"
                  dense
                  outlined
                  placeholder="对外暴露的属性名"
                  @blur="validateExposedName(props.row)"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-targetModule="props">
              <q-td :props="props">
                <q-select
                  v-model="props.row.targetModule"
                  :options="moduleOptions"
                  dense
                  options-dense
                  outlined
                  emit-value
                  map-options
                  placeholder="选择模块"
                  @update:model-value="onModuleChange(props.row)"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-targetAttribute="props">
              <q-td :props="props">
                <q-select
                  v-model="props.row.targetAttribute"
                  :options="getAttributeOptions(props.row.targetModule)"
                  dense
                  options-dense
                  outlined
                  emit-value
                  map-options
                  placeholder="选择属性"
                  :disable="!props.row.targetModule"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-description="props">
              <q-td :props="props">
                <q-input
                  v-model="props.row.description"
                  dense
                  outlined
                  placeholder="描述"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-preview="props">
              <q-td :props="props">
                <div class="redirect-preview">
                  <span v-if="props.row.exposedName && props.row.targetModule && props.row.targetAttribute">
                    {{ props.row.exposedName }} → {{ props.row.targetModule }}.{{ props.row.targetAttribute }}
                  </span>
                  <span v-else class="text-grey-6">未完整配置</span>
                </div>
              </q-td>
            </template>
          </q-table>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="saveAndClose"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, inject, watch } from 'vue'
import { useEditorStore } from 'stores/editor'
import {modelManager, PROC} from "src/utils/modelManager.js";

const editorStore = useEditorStore()
const editorRef = inject('editorRef')

// 表格列定义
const columns = [
  {
    name: 'exposedName',
    label: '对外属性名',
    field: 'exposedName',
    required: true,
    align: 'left',
    sortable: true
  },
  {
    name: 'targetModule',
    label: '目标模块',
    field: 'targetModule',
    align: 'left'
  },
  {
    name: 'targetAttribute',
    label: '目标属性',
    field: 'targetAttribute',
    align: 'left'
  },
  {
    name: 'description',
    label: '描述',
    field: 'description',
    align: 'left'
  },
  {
    name: 'preview',
    label: '重定向预览',
    field: 'preview',
    align: 'left'
  }
]

// 重定向数据 - 使用本地副本避免直接修改store
const redirects = ref([])

// 原始数据备份，用于取消时恢复
const originalRedirects = ref([])

// 选中的行
const selectedRows = ref([])

// 监听对话框打开，初始化数据
watch(() => editorStore.attributeRedirectDialog.visible, (visible) => {
  if (visible) {
    // 深拷贝原始数据
    originalRedirects.value = JSON.parse(JSON.stringify(editorStore.attributeRedirectDialog.redirects || []))
    redirects.value = JSON.parse(JSON.stringify(editorStore.attributeRedirectDialog.redirects || []))
    selectedRows.value = []
  }
})

// 模块选项
const moduleOptions = computed(() => {
  if (!editorRef?.value) return []

  const modelData = editorRef.value.getModelData()
  if (!modelData?.nodes) return []

  return modelData.nodes
    .filter(node => node.data?.name && node.data?.model)
    .map(node => ({
      label: `${node.data.name} (${node.data.model})`,
      value: node.data.name
    }))
})

// 模块属性缓存
const moduleAttributesCache = ref(new Map())

// 获取模块的属性选项
const getAttributeOptions = (moduleName) => {
  if (!moduleName) return []

  const cached = moduleAttributesCache.value.get(moduleName)
  if (cached) return cached

  // 从模型数据中获取模块信息
  const modelData = editorRef?.value?.getModelData()
  if (!modelData?.nodes) return []

  const node = modelData.nodes.find(n => n.data?.name === moduleName)
  if (!node?.data?.model) return []

  // 异步加载属性定义
  loadModuleAttributes(moduleName, node.data.model)

  // 返回默认属性，异步加载完成后会更新
  return [
    // { label: '加载中...', value: '', disable: true }
  ]
}

// 异步加载模块属性
const loadModuleAttributes = async (moduleName, modelName) => {
  try {
    // 尝试从进程模型中获取属性定义
    const processModel = await modelManager.getModel(PROC, modelName)
    if (processModel.attributes && Array.isArray(processModel.attributes)) {
      const attributes = processModel.attributes.map(attr => ({
        label: attr.label || attr.name,
        value: attr.name
      }))
      moduleAttributesCache.value.set(moduleName, attributes)
      return
    }
  } catch (error) {
    console.warn(`无法加载进程模型 ${modelName}:`, error)
  }

  // 如果无法从进程模型获取，设置一些通用属性
  const defaultAttributes = [
    { label: '基本属性1', value: 'basic_attr1' },
    { label: '基本属性2', value: 'basic_attr2' },
    { label: '配置参数', value: 'config_param' }
  ]

  moduleAttributesCache.value.set(moduleName, defaultAttributes)
}

// 添加重定向
const addRedirect = () => {
  const newRedirect = {
    id: Date.now(), // 简单的ID生成
    exposedName: '',
    targetModule: '',
    targetAttribute: '',
    description: ''
  }
  redirects.value.push(newRedirect)
}

// 删除选中的重定向
const deleteSelected = () => {
  selectedRows.value.forEach(row => {
    const index = redirects.value.findIndex(redirect => redirect.id === row.id)
    if (index > -1) {
      redirects.value.splice(index, 1)
    }
  })
  selectedRows.value = []
}

// 验证对外属性名
const validateExposedName = (redirect) => {
  // 检查属性名是否重复
  const duplicates = redirects.value.filter(r => r.exposedName === redirect.exposedName && r.id !== redirect.id)
  if (duplicates.length > 0) {
    // 如果重复，添加后缀
    let counter = 1
    let newName = `${redirect.exposedName}_${counter}`
    while (redirects.value.some(r => r.exposedName === newName && r.id !== redirect.id)) {
      counter++
      newName = `${redirect.exposedName}_${counter}`
    }
    redirect.exposedName = newName
  }
}

// 模块变化时清空属性选择并加载新模块的属性
const onModuleChange = (redirect) => {
  redirect.targetAttribute = ''

  // 触发属性加载
  if (redirect.targetModule) {
    const modelData = editorRef?.value?.getModelData()
    if (modelData?.nodes) {
      const node = modelData.nodes.find(n => n.data?.name === redirect.targetModule)
      if (node?.data?.model) {
        loadModuleAttributes(redirect.targetModule, node.data.model)
      }
    }
  }
}

// 关闭对话框（取消）
const closeDialog = () => {
  // 恢复原始数据
  editorStore.attributeRedirectDialog.redirects = [...originalRedirects.value]
  editorStore.attributeRedirectDialog.visible = false
  selectedRows.value = []

  // 清空本地数据
  redirects.value = []
  originalRedirects.value = []
}

// 保存并关闭
const saveAndClose = () => {
  // 验证所有重定向配置是否完整
  const incompleteRedirects = redirects.value.filter(r =>
    !r.exposedName || !r.targetModule || !r.targetAttribute
  )

  if (incompleteRedirects.length > 0) {
    // 可以显示警告或者过滤掉不完整的配置
    console.warn('存在不完整的重定向配置:', incompleteRedirects)
  }

  // 保存修改到store
  editorStore.attributeRedirectDialog.redirects = [...redirects.value]
  editorStore.attributeRedirectDialog.visible = false
  selectedRows.value = []

  // 清空本地数据
  redirects.value = []
  originalRedirects.value = []
}
</script>

<style scoped>
.attribute-redirect-dialog {
  width: 90vw;
  height: 90vh;
}

.dialog-header {
  background-color: #f5f5f5;
  padding: 16px;
  font-weight: 500;
}

.dialog-content {
  flex: 1;
  padding: 16px;
}

.redirect-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.redirect-table {
  flex: 1;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.redirect-preview {
  font-family: monospace;
  font-size: 12px;
  color: #666;
}
</style>
